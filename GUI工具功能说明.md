# GUI工具功能说明

## 概述

GUI窗口的"工具"菜单下现在包含三个完全实现的功能：

1. **数据质量检查** - 全面分析数据质量并生成详细报告
2. **模型选择建议** - 智能推荐最优模型组合
3. **配置生成器** - 生成各种配置文件

## 功能详细说明

### 1. 数据质量检查

**功能描述：**
- 对加载的数据进行全面的质量分析
- 检测缺失值、重复值、异常值等数据问题
- 生成数据统计摘要和质量评分
- 提供数据改进建议

**使用方法：**
1. 首先在GUI中加载数据文件
2. 点击"工具" → "数据质量检查"
3. 系统会自动分析数据并显示结果
4. 可以保存质量报告到文件

**输出内容：**
- 数据基本信息（形状、内存使用等）
- 数据完整性检查结果
- 异常值检测结果
- 目标变量分布分析
- 数值特征统计摘要
- 数据质量改进建议

### 2. 模型选择建议

**功能描述：**
- 基于数据特征智能推荐最优模型组合
- 评估多个模型的性能和多样性
- 提供权重分配建议
- 支持多种选择策略

**使用方法：**
1. 首先在GUI中加载数据文件
2. 点击"工具" → "模型选择建议"
3. 配置分析参数：
   - 目标模型数量（2-8个）
   - 选择策略（performance/diversity/balanced/quantified）
   - 最低性能阈值（0-1）
   - 相关性阈值（0-1）
4. 点击"开始分析"
5. 查看推荐结果和详细分析

**配置参数说明：**
- **目标模型数量**：希望选择的模型个数
- **选择策略**：
  - performance：优先选择高性能模型
  - diversity：优先选择多样性高的模型
  - balanced：平衡性能和多样性
  - quantified：使用量化多样性评估
- **最低性能阈值**：模型的最低性能要求
- **相关性阈值**：模型间相关性的阈值

**输出内容：**
- 推荐的模型组合
- 组合评分和预期性能提升
- 建议权重分配
- 所有模型的详细性能评估
- 模型多样性分析
- 使用建议

### 3. 配置生成器

**功能描述：**
- 生成多种类型的配置文件
- 支持多数据源集成配置
- 支持训练参数配置
- 支持超参数调优配置

**使用方法：**
1. 点击"工具" → "配置生成器"
2. 选择配置类型：
   - 多数据源集成配置
   - 训练参数配置
   - 超参数调优配置
3. 根据选择的类型填写相应参数
4. 点击"生成配置"保存到文件
5. 可以使用"预览配置"查看内容

**配置类型说明：**

#### 多数据源集成配置
- 创建模型-数据文件映射
- 支持添加/删除映射关系
- 生成JSON格式配置文件
- 可用于多数据源集成学习

#### 训练参数配置
- 测试集比例设置
- 随机种子配置
- 特征缩放方法选择
- 交叉验证折数设置

#### 超参数调优配置
- 试验次数设置
- 调优策略选择（TPE/Random/CmaEs）
- 超时时间配置
- 评估指标选择

## 技术实现

### 数据质量检查
- 使用 `data_validation_eda.py` 模块
- 调用 `run_complete_data_analysis` 函数
- 支持多线程后台执行
- 生成HTML格式的详细报告

### 模型选择建议
- 使用 `enhanced_ensemble_selector.py` 模块
- 支持多种选择策略和评估指标
- 包含量化多样性评估
- 提供详细的性能和多样性分析

### 配置生成器
- 支持三种配置类型的生成
- JSON格式输出，便于程序读取
- 包含使用示例和说明信息
- 支持配置预览功能

## 使用建议

1. **数据质量检查**：
   - 在开始模型训练前使用
   - 根据建议处理数据问题
   - 定期检查数据质量

2. **模型选择建议**：
   - 在不确定使用哪些模型时使用
   - 可以尝试不同的选择策略
   - 参考权重分配进行集成学习

3. **配置生成器**：
   - 用于批量实验和自动化
   - 保存常用配置以便重复使用
   - 与命令行工具配合使用

## 注意事项

1. 数据质量检查需要先加载数据文件
2. 模型选择建议会训练多个模型，可能需要较长时间
3. 配置文件生成后可以在命令行模式中使用
4. 所有功能都支持中文界面和错误提示
5. 建议在使用前确保数据格式正确

## 错误处理

- 所有功能都包含完善的错误处理机制
- 会显示详细的错误信息和解决建议
- 支持后台任务的异常处理
- 提供用户友好的错误提示

这三个工具功能大大增强了GUI的实用性，为用户提供了完整的数据分析和模型选择工作流程。
